"use client"

import { useState } from "react"
import { ReviewForm } from "@/components/review-form"
import { ReviewList } from "@/components/review-list"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"

export interface Review {
  id: string
  customerName: string
  customerAvatar?: string
  rating: number
  title: string
  content: string
  date: string
  replies: Reply[]
}

export interface Reply {
  id: string
  authorName: string
  authorAvatar?: string
  content: string
  date: string
  isBusinessOwner?: boolean
}

const initialReviews: Review[] = [
  {
    id: "1",
    customerName: "<PERSON>",
    customerAvatar: "/placeholder.svg?height=40&width=40",
    rating: 5,
    title: "Excellent service!",
    content:
      "I had an amazing experience with this company. The staff was professional, friendly, and went above and beyond to meet my needs. Highly recommended!",
    date: "2024-01-15",
    replies: [
      {
        id: "r1",
        authorName: "Business Owner",
        content:
          "Thank you so much for your kind words, <PERSON>! We're thrilled to hear about your positive experience. We look forward to serving you again soon!",
        date: "2024-01-16",
        isBusinessOwner: true,
      },
    ],
  },
  {
    id: "2",
    customerName: "Mike Chen",
    customerAvatar: "/placeholder.svg?height=40&width=40",
    rating: 4,
    title: "Great product, minor issues",
    content:
      "Overall very satisfied with the product quality. There were some minor delivery delays, but the customer service team was responsive and helpful in resolving the issues.",
    date: "2024-01-10",
    replies: [
      {
        id: "r2",
        authorName: "Customer Service Team",
        content:
          "Hi Mike, we apologize for the delivery delays and appreciate your patience. We're working on improving our logistics to prevent such issues in the future. Thank you for your feedback!",
        date: "2024-01-11",
        isBusinessOwner: true,
      },
      {
        id: "r3",
        authorName: "Jennifer Smith",
        content: "I had a similar experience with delivery, but the product quality is definitely worth it!",
        date: "2024-01-12",
        isBusinessOwner: false,
      },
    ],
  },
  {
    id: "3",
    customerName: "Alex Rodriguez",
    customerAvatar: "/placeholder.svg?height=40&width=40",
    rating: 3,
    title: "Average experience",
    content:
      "The service was okay, nothing exceptional but not bad either. Room for improvement in communication and response times.",
    date: "2024-01-08",
    replies: [],
  },
]

export default function ReviewService() {
  const [reviews, setReviews] = useState<Review[]>(initialReviews)

  const handleNewReview = (newReview: Omit<Review, "id" | "replies">) => {
    const review: Review = {
      ...newReview,
      id: Date.now().toString(),
      replies: [],
    }
    setReviews([review, ...reviews])
  }

  const handleNewReply = (reviewId: string, reply: Omit<Reply, "id">) => {
    const newReply: Reply = {
      ...reply,
      id: Date.now().toString(),
    }

    setReviews(
      reviews.map((review) =>
        review.id === reviewId ? { ...review, replies: [...review.replies, newReply] } : review,
      ),
    )
  }

  const averageRating =
    reviews.length > 0 ? (reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length).toFixed(1) : "0.0"

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Customer Reviews</h1>
        <p className="text-muted-foreground">Share your experience and read what others have to say</p>
      </div>

      <div className="grid gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Review Summary</CardTitle>
            <CardDescription>
              {reviews.length} reviews • Average rating: {averageRating}/5.0
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="text-3xl font-bold">{averageRating}</div>
              <div className="flex-1">
                {[5, 4, 3, 2, 1].map((rating) => {
                  const count = reviews.filter((r) => r.rating === rating).length
                  const percentage = reviews.length > 0 ? (count / reviews.length) * 100 : 0
                  return (
                    <div key={rating} className="flex items-center gap-2 text-sm">
                      <span className="w-3">{rating}</span>
                      <div className="flex-1 bg-muted rounded-full h-2">
                        <div
                          className="bg-primary h-2 rounded-full transition-all"
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                      <span className="w-8 text-muted-foreground">{count}</span>
                    </div>
                  )
                })}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="reviews" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="reviews">All Reviews</TabsTrigger>
          <TabsTrigger value="write">Write a Review</TabsTrigger>
        </TabsList>

        <TabsContent value="reviews" className="space-y-6">
          <ReviewList reviews={reviews} onReply={handleNewReply} />
        </TabsContent>

        <TabsContent value="write">
          <ReviewForm onSubmit={handleNewReview} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
"use client"

import type React from "react"

import { useState } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Star, MessageCircle, Send } from "lucide-react"
import type { Review, Reply } from "@/app/page"

interface ReviewCardProps {
  review: Review
  onReply: (reviewId: string, reply: Omit<Reply, "id">) => void
}

export function ReviewCard({ review, onReply }: ReviewCardProps) {
  const [showReplyForm, setShowReplyForm] = useState(false)
  const [replyContent, setReplyContent] = useState("")
  const [replyAuthor, setReplyAuthor] = useState("")

  const handleReplySubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!replyContent.trim() || !replyAuthor.trim()) return

    onReply(review.id, {
      authorName: replyAuthor.trim(),
      content: replyContent.trim(),
      date: new Date().toISOString().split("T")[0],
      isBusinessOwner: false,
    })

    setReplyContent("")
    setReplyAuthor("")
    setShowReplyForm(false)
  }

  return (
    <Card>
      <CardHeader className="pb-4">
        <div className="flex items-start gap-4">
          <Avatar>
            <AvatarImage src={review.customerAvatar || "/placeholder.svg"} alt={review.customerName} />
            <AvatarFallback>
              {review.customerName
                .split(" ")
                .map((n) => n[0])
                .join("")
                .toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold">{review.customerName}</h3>
              <span className="text-sm text-muted-foreground">{review.date}</span>
            </div>
            <div className="flex items-center gap-2 mb-2">
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={`w-4 h-4 ${
                      star <= review.rating ? "fill-primary text-primary" : "text-muted-foreground"
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm font-medium">{review.rating}/5</span>
            </div>
            <h4 className="font-medium mb-2">{review.title}</h4>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-muted-foreground leading-relaxed">{review.content}</p>

        {review.replies.length > 0 && (
          <div className="space-y-4">
            <Separator />
            <div className="space-y-4">
              {review.replies.map((reply) => (
                <div key={reply.id} className="flex gap-3 pl-4 border-l-2 border-muted">
                  <Avatar className="w-8 h-8">
                    <AvatarImage src={reply.authorAvatar || "/placeholder.svg"} alt={reply.authorName} />
                    <AvatarFallback className="text-xs">
                      {reply.authorName
                        .split(" ")
                        .map((n) => n[0])
                        .join("")
                        .toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-sm">{reply.authorName}</span>
                      {reply.isBusinessOwner && (
                        <Badge variant="secondary" className="text-xs">
                          Business
                        </Badge>
                      )}
                      <span className="text-xs text-muted-foreground">{reply.date}</span>
                    </div>
                    <p className="text-sm text-muted-foreground">{reply.content}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="flex items-center gap-2 pt-2">
          <Button variant="ghost" size="sm" onClick={() => setShowReplyForm(!showReplyForm)} className="gap-2">
            <MessageCircle className="w-4 h-4" />
            Reply
          </Button>
          <span className="text-sm text-muted-foreground">
            {review.replies.length} {review.replies.length === 1 ? "reply" : "replies"}
          </span>
        </div>

        {showReplyForm && (
          <form onSubmit={handleReplySubmit} className="space-y-3 pt-2 border-t">
            <Input
              placeholder="Your name"
              value={replyAuthor}
              onChange={(e) => setReplyAuthor(e.target.value)}
              required
            />
            <Textarea
              placeholder="Write your reply..."
              value={replyContent}
              onChange={(e) => setReplyContent(e.target.value)}
              rows={3}
              required
            />
            <div className="flex gap-2">
              <Button type="submit" size="sm" className="gap-2">
                <Send className="w-4 h-4" />
                Post Reply
              </Button>
              <Button type="button" variant="ghost" size="sm" onClick={() => setShowReplyForm(false)}>
                Cancel
              </Button>
            </div>
          </form>
        )}
      </CardContent>
    </Card>
  )
}
"use client"

import type React from "react"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Star } from "lucide-react"
import type { Review } from "@/app/page"

interface ReviewFormProps {
  onSubmit: (review: Omit<Review, "id" | "replies">) => void
}

export function ReviewForm({ onSubmit }: ReviewFormProps) {
  const [rating, setRating] = useState(0)
  const [hoveredRating, setHoveredRating] = useState(0)
  const [customerName, setCustomerName] = useState("")
  const [title, setTitle] = useState("")
  const [content, setContent] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (rating === 0 || !customerName.trim() || !title.trim() || !content.trim()) {
      return
    }

    onSubmit({
      customerName: customerName.trim(),
      rating,
      title: title.trim(),
      content: content.trim(),
      date: new Date().toISOString().split("T")[0],
    })

    // Reset form
    setRating(0)
    setHoveredRating(0)
    setCustomerName("")
    setTitle("")
    setContent("")
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Write a Review</CardTitle>
        <CardDescription>Share your experience to help others make informed decisions</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="name">Your Name</Label>
            <Input
              id="name"
              value={customerName}
              onChange={(e) => setCustomerName(e.target.value)}
              placeholder="Enter your name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label>Rating</Label>
            <div className="flex gap-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  className="p-1 hover:scale-110 transition-transform"
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  onClick={() => setRating(star)}
                >
                  <Star
                    className={`w-6 h-6 ${
                      star <= (hoveredRating || rating) ? "fill-primary text-primary" : "text-muted-foreground"
                    }`}
                  />
                </button>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="title">Review Title</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Summarize your experience"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="content">Your Review</Label>
            <Textarea
              id="content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="Tell us about your experience in detail..."
              rows={4}
              required
            />
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={rating === 0 || !customerName.trim() || !title.trim() || !content.trim()}
          >
            Submit Review
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
