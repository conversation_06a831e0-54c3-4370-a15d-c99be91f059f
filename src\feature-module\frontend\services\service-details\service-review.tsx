"use client"

import { useState } from "react"
import { ReviewForm } from "./components/review-form.tsx"
import { ReviewList } from "./components/review-list.tsx"
import { <PERSON>, CardBody, CardHeader, Tabs, Tab } from "@heroui/react"

export interface Review {
  id: string
  customerName: string
  customerAvatar?: string
  rating: number
  title: string
  content: string
  date: string
  replies: Reply[]
}

export interface Reply {
  id: string
  authorName: string
  authorAvatar?: string
  content: string
  date: string
  isBusinessOwner?: boolean
}

const initialReviews: Review[] = [
  {
    id: "1",
    customerName: "<PERSON>",
    customerAvatar: "/placeholder.svg?height=40&width=40",
    rating: 5,
    title: "Excellent service!",
    content:
      "I had an amazing experience with this company. The staff was professional, friendly, and went above and beyond to meet my needs. Highly recommended!",
    date: "2024-01-15",
    replies: [
      {
        id: "r1",
        authorName: "Business Owner",
        content:
          "Thank you so much for your kind words, <PERSON>! We're thrilled to hear about your positive experience. We look forward to serving you again soon!",
        date: "2024-01-16",
        isBusinessOwner: true,
      },
    ],
  },
  {
    id: "2",
    customerName: "<PERSON>",
    customerAvatar: "/placeholder.svg?height=40&width=40",
    rating: 4,
    title: "Great product, minor issues",
    content:
      "Overall very satisfied with the product quality. There were some minor delivery delays, but the customer service team was responsive and helpful in resolving the issues.",
    date: "2024-01-10",
    replies: [
      {
        id: "r2",
        authorName: "Customer Service Team",
        content:
          "Hi Mike, we apologize for the delivery delays and appreciate your patience. We're working on improving our logistics to prevent such issues in the future. Thank you for your feedback!",
        date: "2024-01-11",
        isBusinessOwner: true,
      },
      {
        id: "r3",
        authorName: "Jennifer Smith",
        content: "I had a similar experience with delivery, but the product quality is definitely worth it!",
        date: "2024-01-12",
        isBusinessOwner: false,
      },
    ],
  },
  {
    id: "3",
    customerName: "Alex Rodriguez",
    customerAvatar: "/placeholder.svg?height=40&width=40",
    rating: 3,
    title: "Average experience",
    content:
      "The service was okay, nothing exceptional but not bad either. Room for improvement in communication and response times.",
    date: "2024-01-08",
    replies: [],
  },
]

export default function ReviewService() {
  const [reviews, setReviews] = useState<Review[]>(initialReviews)

  const handleNewReview = (newReview: Omit<Review, "id" | "replies">) => {
    const review: Review = {
      ...newReview,
      id: Date.now().toString(),
      replies: [],
    }
    setReviews([review, ...reviews])
  }

  const handleNewReply = (reviewId: string, reply: Omit<Reply, "id">) => {
    const newReply: Reply = {
      ...reply,
      id: Date.now().toString(),
    }

    setReviews(
      reviews.map((review) =>
        review.id === reviewId ? { ...review, replies: [...review.replies, newReply] } : review,
      ),
    )
  }

  const averageRating =
    reviews.length > 0 ? (reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length).toFixed(1) : "0.0"

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Customer Reviews</h1>
        <p className="text-gray-600">Share your experience and read what others have to say</p>
      </div>

      <div className="grid gap-6 mb-8">
        <Card>
          <CardHeader>
            <div>
              <h3 className="text-lg font-semibold">Review Summary</h3>
              <p className="text-sm text-gray-600">
                {reviews.length} reviews • Average rating: {averageRating}/5.0
              </p>
            </div>
          </CardHeader>
          <CardBody>
            <div className="flex items-center gap-4">
              <div className="text-3xl font-bold">{averageRating}</div>
              <div className="flex-1">
                {[5, 4, 3, 2, 1].map((rating) => {
                  const count = reviews.filter((r) => r.rating === rating).length
                  const percentage = reviews.length > 0 ? (count / reviews.length) * 100 : 0
                  return (
                    <div key={rating} className="flex items-center gap-2 text-sm">
                      <span className="w-3">{rating}</span>
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full transition-all"
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                      <span className="w-8 text-gray-500">{count}</span>
                    </div>
                  )
                })}
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      <Tabs defaultSelectedKey="reviews" className="space-y-6">
        <Tab key="reviews" title="All Reviews">
          <div className="space-y-6">
            <ReviewList reviews={reviews} onReply={handleNewReply} />
          </div>
        </Tab>
        <Tab key="write" title="Write a Review">
          <ReviewForm onSubmit={handleNewReview} />
        </Tab>
      </Tabs>
    </div>
  )
}

