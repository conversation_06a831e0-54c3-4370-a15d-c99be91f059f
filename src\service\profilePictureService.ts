import { apiClient } from '../api';
import { 
  uploadProfilePictureToS3, 
  getProfilePictureUrl, 
  deleteProfilePictureFromS3 
} from '../feature-module/frontend/Customer/aws/s3FileUpload';

// Profile picture interfaces
export interface ProfilePictureUploadResult {
  imageName: string;
  fullUrl: string;
  key: string;
  originalName: string;
  size: number;
  timestamp: number;
  userId: string;
}

export interface ProfilePictureUpdateRequest {
  profileImage: string; // Image name to store in database
}

export interface UserProfileData {
  name?: string;
  email?: string;
  profileImage?: string;
  [key: string]: any;
}

/**
 * Upload profile picture to S3 and update user profile in database
 */
export const uploadAndSaveProfilePicture = async (
  file: File, 
  userId: string, 
  currentProfileImage?: string
): Promise<ProfilePictureUploadResult> => {
  try {
    console.log('Starting profile picture upload process...', {
      fileName: file.name,
      fileSize: file.size,
      userId,
      currentProfileImage
    });

    // Step 1: Upload new profile picture to S3
    const uploadResult = await uploadProfilePictureToS3(file, userId);
    
    if (!uploadResult.imageName || !uploadResult.fullUrl) {
      throw new Error('Failed to upload profile picture to S3');
    }

    console.log('Profile picture uploaded to S3 successfully:', uploadResult);

    // Step 2: Update user profile in database with new image name
    const updateData: ProfilePictureUpdateRequest = {
      profileImage: uploadResult.imageName // Store only the image name
    };

    const response = await apiClient.put(`/api/v1/user/${userId}`, updateData);

    if (response.status !== 200) {
      // If database update fails, we should ideally delete the uploaded image
      // But for now, we'll just log the error and throw
      console.error('Failed to update user profile in database:', response);
      throw new Error(`Failed to update user profile. Server returned status ${response.status}`);
    }

    console.log('User profile updated in database successfully');

    // Step 3: Delete old profile picture from S3 (if exists)
    if (currentProfileImage && currentProfileImage !== uploadResult.imageName) {
      console.log('Deleting old profile picture:', currentProfileImage);
      await deleteProfilePictureFromS3(currentProfileImage);
    }

    return uploadResult;
  } catch (error) {
    console.error('Error in profile picture upload process:', error);
    throw error;
  }
};

/**
 * Get profile picture URL from image name
 */
export const getProfilePictureUrlFromName = (imageName: string): string => {
  return getProfilePictureUrl(imageName);
};

/**
 * Delete profile picture from S3 and update user profile
 */
export const deleteProfilePicture = async (
  userId: string, 
  imageName: string
): Promise<boolean> => {
  try {
    console.log('Starting profile picture deletion process...', {
      userId,
      imageName
    });

    // Step 1: Update user profile in database to remove profile image
    const updateData: ProfilePictureUpdateRequest = {
      profileImage: '' // Clear the profile image
    };

    const response = await apiClient.put(`/api/v1/user/${userId}`, updateData);

    if (response.status !== 200) {
      console.error('Failed to update user profile in database:', response);
      throw new Error(`Failed to update user profile. Server returned status ${response.status}`);
    }

    console.log('User profile updated in database successfully');

    // Step 2: Delete profile picture from S3
    if (imageName) {
      await deleteProfilePictureFromS3(imageName);
    }

    return true;
  } catch (error) {
    console.error('Error in profile picture deletion process:', error);
    throw error;
  }
};

/**
 * Get user profile data including profile picture URL
 */
export const getUserProfileWithPicture = async (userId: string): Promise<UserProfileData | null> => {
  try {
    console.log(`Fetching user profile data for ID: ${userId}`);

    const response = await apiClient.get(`/api/v1/user/${userId}`);

    if (response.status === 200 && response.data) {
      const userData = response.data.user || response.data;
      
      // Convert profile image name to full URL if it exists
      if (userData.profileImage) {
        userData.profileImageUrl = getProfilePictureUrlFromName(userData.profileImage);
      }

      console.log('User profile data fetched successfully:', userData);
      return userData;
    }

    return null;
  } catch (error) {
    console.error('Error fetching user profile data:', error);
    throw error;
  }
};

/**
 * Validate profile picture file
 */
export const validateProfilePictureFile = (file: File): { isValid: boolean; error?: string } => {
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.'
    };
  }

  // Check file size (max 5MB)
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'File size too large. Maximum size is 5MB.'
    };
  }

  // Check if file is actually an image
  if (!file.type.startsWith('image/')) {
    return {
      isValid: false,
      error: 'Selected file is not an image.'
    };
  }

  return { isValid: true };
};

/**
 * Generate fallback profile picture URL
 */
export const getFallbackProfilePicture = (name?: string): string => {
  // You can customize this to generate avatar based on user name or use a default
  if (name) {
    // Generate a simple avatar URL based on name initials
    const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=0D8ABC&color=fff&size=200`;
  }
  
  // Default fallback image
  return '/fallback-profile.webp';
};
