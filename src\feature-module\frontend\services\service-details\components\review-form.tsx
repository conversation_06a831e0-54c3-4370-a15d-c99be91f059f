"use client"

import type React from "react"
import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>, CardBody, CardHeader, Input, Textarea } from "@heroui/react"
import { Star } from "lucide-react"
import type { Review } from "../service-review"

interface ReviewFormProps {
  onSubmit: (review: Omit<Review, "id" | "replies">) => void
}

export function ReviewForm({ onSubmit }: ReviewFormProps) {
  const [rating, setRating] = useState(0)
  const [hoveredRating, setHoveredRating] = useState(0)
  const [customerName, setCustomerName] = useState("")
  const [title, setTitle] = useState("")
  const [content, setContent] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (rating === 0 || !customerName.trim() || !title.trim() || !content.trim()) {
      return
    }

    onSubmit({
      customerName: customerName.trim(),
      rating,
      title: title.trim(),
      content: content.trim(),
      date: new Date().toISOString().split("T")[0],
    })

    // Reset form
    setRating(0)
    setHoveredRating(0)
    setCustomerName("")
    setTitle("")
    setContent("")
  }

  return (
    <Card>
      <CardHeader>
        <div>
          <h3 className="text-lg font-semibold">Write a Review</h3>
          <p className="text-sm text-gray-600">Share your experience to help others make informed decisions</p>
        </div>
      </CardHeader>
      <CardBody>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Input
              id="name"
              label="Your Name"
              value={customerName}
              onValueChange={setCustomerName}
              placeholder="Enter your name"
              isRequired
              variant="bordered"
              labelPlacement="outside"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Rating</label>
            <div className="flex gap-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  className="p-1 hover:scale-110 transition-transform"
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  onClick={() => setRating(star)}
                >
                  <Star
                    className={`w-6 h-6 ${
                      star <= (hoveredRating || rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                    }`}
                  />
                </button>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Input
              id="title"
              label="Review Title"
              value={title}
              onValueChange={setTitle}
              placeholder="Summarize your experience"
              isRequired
              variant="bordered"
              labelPlacement="outside"
            />
          </div>

          <div className="space-y-2">
            <Textarea
              id="content"
              label="Your Review"
              value={content}
              onValueChange={setContent}
              placeholder="Tell us about your experience in detail..."
              minRows={4}
              isRequired
              variant="bordered"
              labelPlacement="outside"
            />
          </div>

          <Button
            type="submit"
            color="primary"
            className="w-full"
            isDisabled={rating === 0 || !customerName.trim() || !title.trim() || !content.trim()}
          >
            Submit Review
          </Button>
        </form>
      </CardBody>
    </Card>
  )
}
