import { apiClient } from '../api';
import { Path } from '../api/backendUrl';
import logger from '../utils/logger';
import { CategoriesAndSubResponse, CategoriesResponse, Category } from '../utils/type';

interface SubCategory {
  subCategoryId: string;
  subCategoryName: string;
  subCategorySlug: string;
  categoryName: string;
  categoryId: string;
  createdAt: string;
  isFeatured: boolean;
  isCertificateRequired: boolean;
}



interface SubCategoriesResponse {
  success: boolean;
  subCategories: SubCategory[];
  total: number;
  page: number;
  pages: number;
}

export const getAllCategory = async (): Promise<CategoriesResponse> => {
  try {
    const res = await apiClient.get(Path.getAllCategory);
    return res.data;
  } catch (error: any) {
    logger.error('Error fetching categories:', error);
    throw error;
  }
};
export const getAllCategoryAndSub = async (): Promise<CategoriesAndSubResponse> => {
  try {
    // Make two separate API calls and combine the results
    const [categoriesRes, subCategoriesRes] = await Promise.all([
      apiClient.get(Path.getAllCategory),
      apiClient.get(Path.getAllSubCategory)
    ]);

    const categories = categoriesRes.data.categories || [];
    const subCategories = subCategoriesRes.data.subCategories || [];

    // Combine categories with their subcategories
    const categoriesWithSub = categories.map((category: Category) => ({
      ...category,
      subCategories: subCategories.filter((sub: SubCategory) => sub.categoryId === category.categoryId)
    }));

    const combinedResponse = {
      success: true,
      categoriesWithSubCategories: categoriesWithSub,
      total: categories.length,
      page: 1,
      pages: 1
    };

    console.log("Combined RES: ", combinedResponse);
    return combinedResponse;
  } catch (error: any) {
    logger.error('Error fetching categories and subcategories:', error);
    throw error;
  }
};

export const getAllSubCategory = async (): Promise<SubCategoriesResponse> => {
  try {
    const res = await apiClient.get(Path.getAllSubCategory);
    return res.data;
  } catch (error: any) {
    logger.error('Error fetching subcategories:', error);
    throw error;
  }
};

export const getCategoryById = async (id: string) => {
  try {
    const res = await apiClient.get(`${Path.getAllCategory}/${id}`);
    return res.data;
  } catch (error: any) {
    console.log("ERROR: ", error);
    // logger.error(error);
    throw error;
  }
};