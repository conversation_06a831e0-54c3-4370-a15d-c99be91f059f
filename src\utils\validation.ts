/**
 * Comprehensive validation utilities for form fields
 * Used across the application for consistent validation
 */

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  customValidator?: (value: string) => ValidationResult;
}

/**
 * Validates a single field based on provided rules
 */
export const validateField = (value: string, rules: ValidationRule): ValidationResult => {
  const trimmedValue = value.trim();

  // Check required field
  if (rules.required && !trimmedValue) {
    return { isValid: false, error: 'This field is required' };
  }

  // If field is empty and not required, it's valid
  if (!trimmedValue && !rules.required) {
    return { isValid: true };
  }

  // Check minimum length
  if (rules.minLength && trimmedValue.length < rules.minLength) {
    return { 
      isValid: false, 
      error: `Must be at least ${rules.minLength} characters long` 
    };
  }

  // Check maximum length
  if (rules.maxLength && trimmedValue.length > rules.maxLength) {
    return { 
      isValid: false, 
      error: `Must be less than ${rules.maxLength} characters` 
    };
  }

  // Check pattern
  if (rules.pattern && !rules.pattern.test(trimmedValue)) {
    return { isValid: false, error: 'Invalid format' };
  }

  // Run custom validator
  if (rules.customValidator) {
    return rules.customValidator(trimmedValue);
  }

  return { isValid: true };
};

/**
 * Predefined validation rules for common fields
 */
export const ValidationRules = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    pattern: /^[a-zA-Z\s'-]+$/,
    customValidator: (value: string): ValidationResult => {
      if (!/^[a-zA-Z\s'-]+$/.test(value)) {
        return { 
          isValid: false, 
          error: 'Name can only contain letters, spaces, hyphens, and apostrophes' 
        };
      }
      return { isValid: true };
    }
  },

  email: {
    required: true,
    maxLength: 254,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    customValidator: (value: string): ValidationResult => {
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        return { isValid: false, error: 'Please enter a valid email address' };
      }
      return { isValid: true };
    }
  },

  phone: {
    required: false,
    customValidator: (value: string): ValidationResult => {
      if (!value) return { isValid: true };
      
      const phoneRegex = /^\+?[1-9]\d{1,14}$/;
      const cleanPhone = value.replace(/\D/g, '');
      
      if (cleanPhone.length < 8) {
        return { isValid: false, error: 'Phone number must be at least 8 digits' };
      }
      if (cleanPhone.length > 15) {
        return { isValid: false, error: 'Phone number must be less than 16 digits' };
      }
      if (!phoneRegex.test(value.replace(/\s/g, ''))) {
        return { isValid: false, error: 'Please enter a valid phone number' };
      }
      return { isValid: true };
    }
  },

  bio: {
    required: false,
    maxLength: 500,
    customValidator: (value: string): ValidationResult => {
      if (!value) return { isValid: true };
      
      if (value.length > 500) {
        return { isValid: false, error: 'Bio must be less than 500 characters' };
      }
      if (value.trim().length > 0 && value.trim().length < 10) {
        return { isValid: false, error: 'Bio must be at least 10 characters if provided' };
      }
      return { isValid: true };
    }
  },

  address: {
    required: false,
    minLength: 5,
    maxLength: 100
  },

  city: {
    required: false,
    minLength: 2,
    maxLength: 50,
    pattern: /^[a-zA-Z\s'-]+$/,
    customValidator: (value: string): ValidationResult => {
      if (!value) return { isValid: true };
      
      if (!/^[a-zA-Z\s'-]+$/.test(value)) {
        return { 
          isValid: false, 
          error: 'City can only contain letters, spaces, hyphens, and apostrophes' 
        };
      }
      return { isValid: true };
    }
  },

  country: {
    required: false,
    minLength: 2,
    maxLength: 50,
    pattern: /^[a-zA-Z\s'-]+$/,
    customValidator: (value: string): ValidationResult => {
      if (!value) return { isValid: true };
      
      if (!/^[a-zA-Z\s'-]+$/.test(value)) {
        return { 
          isValid: false, 
          error: 'Country can only contain letters, spaces, hyphens, and apostrophes' 
        };
      }
      return { isValid: true };
    }
  },

  state: {
    required: false,
    minLength: 2,
    maxLength: 50,
    pattern: /^[a-zA-Z\s'-]+$/,
    customValidator: (value: string): ValidationResult => {
      if (!value) return { isValid: true };
      
      if (!/^[a-zA-Z\s'-]+$/.test(value)) {
        return { 
          isValid: false, 
          error: 'State/Province can only contain letters, spaces, hyphens, and apostrophes' 
        };
      }
      return { isValid: true };
    }
  },

  postalCode: {
    required: false,
    minLength: 3,
    maxLength: 10,
    pattern: /^[a-zA-Z0-9\s-]+$/,
    customValidator: (value: string): ValidationResult => {
      if (!value) return { isValid: true };
      
      if (!/^[a-zA-Z0-9\s-]+$/.test(value)) {
        return { 
          isValid: false, 
          error: 'Postal code can only contain letters, numbers, spaces, and hyphens' 
        };
      }
      return { isValid: true };
    }
  }
};

/**
 * Validates date of birth
 */
export const validateDateOfBirth = (dateString: string): ValidationResult => {
  if (!dateString) return { isValid: true };

  const birthDate = new Date(dateString);
  const today = new Date();
  const age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (birthDate > today) {
    return { isValid: false, error: 'Date of birth cannot be in the future' };
  }
  
  if (age < 13 || (age === 13 && monthDiff < 0)) {
    return { isValid: false, error: 'You must be at least 13 years old' };
  }
  
  if (age > 120) {
    return { isValid: false, error: 'Please enter a valid date of birth' };
  }

  return { isValid: true };
};

/**
 * Validates multiple fields at once
 */
export const validateForm = (
  formData: Record<string, string>, 
  fieldRules: Record<string, ValidationRule>
): Record<string, string> => {
  const errors: Record<string, string> = {};

  Object.entries(fieldRules).forEach(([fieldName, rules]) => {
    const value = formData[fieldName] || '';
    const result = validateField(value, rules);
    
    if (!result.isValid && result.error) {
      errors[fieldName] = result.error;
    }
  });

  return errors;
};

/**
 * Sanitizes phone number input
 */
export const sanitizePhoneNumber = (input: string): string => {
  const sanitized = input.startsWith('+')
    ? '+' + input.slice(1).replace(/\D/g, '')
    : input.replace(/\D/g, '');
  return sanitized.slice(0, 16);
};

/**
 * Formats field names for display in error messages
 */
export const formatFieldName = (fieldName: string): string => {
  return fieldName
    .split('.')
    .map(part => part.charAt(0).toUpperCase() + part.slice(1))
    .join(' ');
};
